const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Comprehensive questions for each category and test type
const questionsData = {
  // Class 10 Pass - Aptitude Questions
  CLASS_10_PASS_APTITUDE: [
    { text: "If 2x + 3 = 11, what is the value of x?", options: ["2", "3", "4", "5"], correctAnswer: "4", difficulty: 1 },
    { text: "What is 15% of 200?", options: ["25", "30", "35", "40"], correctAnswer: "30", difficulty: 1 },
    { text: "If a train travels 120 km in 2 hours, what is its speed?", options: ["50 km/h", "60 km/h", "70 km/h", "80 km/h"], correctAnswer: "60 km/h", difficulty: 2 },
    { text: "Which number comes next: 2, 6, 18, 54, ?", options: ["108", "162", "216", "270"], correctAnswer: "162", difficulty: 3 },
    { text: "If area of square is 64 sq cm, what is perimeter?", options: ["16 cm", "24 cm", "32 cm", "48 cm"], correctAnswer: "32 cm", difficulty: 2 },
    { text: "Next number in series: 1, 4, 9, 16, ?", options: ["20", "25", "30", "36"], correctAnswer: "25", difficulty: 2 },
    { text: "If 3 books cost Rs. 45, how much do 7 books cost?", options: ["Rs. 95", "Rs. 105", "Rs. 115", "Rs. 125"], correctAnswer: "Rs. 105", difficulty: 2 },
    { text: "What is 2³ + 3²?", options: ["15", "17", "19", "21"], correctAnswer: "17", difficulty: 2 },
    { text: "If today is Wednesday, what day after 15 days?", options: ["Monday", "Tuesday", "Wednesday", "Thursday"], correctAnswer: "Thursday", difficulty: 3 },
    { text: "Average of 12, 15, 18, and 21?", options: ["16", "16.5", "17", "17.5"], correctAnswer: "16.5", difficulty: 2 },
    { text: "Rectangle: length 8cm, width 5cm. Area?", options: ["30 sq cm", "35 sq cm", "40 sq cm", "45 sq cm"], correctAnswer: "40 sq cm", difficulty: 1 },
    { text: "What is 25% of 80?", options: ["15", "20", "25", "30"], correctAnswer: "20", difficulty: 1 },
    { text: "If 5x - 7 = 23, what is x?", options: ["4", "5", "6", "7"], correctAnswer: "6", difficulty: 2 },
    { text: "LCM of 12 and 18?", options: ["30", "36", "42", "48"], correctAnswer: "36", difficulty: 3 },
    { text: "Cost Rs.100, Sell Rs.120. Profit %?", options: ["15%", "20%", "25%", "30%"], correctAnswer: "20%", difficulty: 3 }
  ],

  // Class 10 Fail - Aptitude Questions (Basic level)
  CLASS_10_FAIL_APTITUDE: [
    { text: "What is 5 + 7?", options: ["10", "11", "12", "13"], correctAnswer: "12", difficulty: 1 },
    { text: "If x + 4 = 9, what is x?", options: ["3", "4", "5", "6"], correctAnswer: "5", difficulty: 1 },
    { text: "What is 10% of 50?", options: ["3", "4", "5", "6"], correctAnswer: "5", difficulty: 1 },
    { text: "How many sides does a triangle have?", options: ["2", "3", "4", "5"], correctAnswer: "3", difficulty: 1 },
    { text: "What is 8 × 6?", options: ["42", "46", "48", "52"], correctAnswer: "48", difficulty: 1 },
    { text: "If you have 20 apples and eat 5, how many left?", options: ["13", "14", "15", "16"], correctAnswer: "15", difficulty: 1 },
    { text: "What comes after 19?", options: ["18", "20", "21", "22"], correctAnswer: "20", difficulty: 1 },
    { text: "How many minutes in 1 hour?", options: ["50", "60", "70", "80"], correctAnswer: "60", difficulty: 1 },
    { text: "What is half of 16?", options: ["6", "7", "8", "9"], correctAnswer: "8", difficulty: 1 },
    { text: "If today is Monday, what is tomorrow?", options: ["Sunday", "Monday", "Tuesday", "Wednesday"], correctAnswer: "Tuesday", difficulty: 1 },
    { text: "What is 15 - 8?", options: ["5", "6", "7", "8"], correctAnswer: "7", difficulty: 1 },
    { text: "How many days in a week?", options: ["5", "6", "7", "8"], correctAnswer: "7", difficulty: 1 },
    { text: "What is 3 × 4?", options: ["10", "11", "12", "13"], correctAnswer: "12", difficulty: 1 },
    { text: "If you buy 2 pens for Rs. 10, cost of 1 pen?", options: ["Rs. 3", "Rs. 4", "Rs. 5", "Rs. 6"], correctAnswer: "Rs. 5", difficulty: 2 },
    { text: "What is bigger: 25 or 52?", options: ["25", "52", "Both same", "Cannot tell"], correctAnswer: "52", difficulty: 1 }
  ],

  // Class 12 Pass - Aptitude Questions (Advanced)
  CLASS_12_PASS_APTITUDE: [
    { text: "If log₂(8) = x, what is x?", options: ["2", "3", "4", "8"], correctAnswer: "3", difficulty: 3 },
    { text: "Derivative of x² + 3x is:", options: ["2x + 3", "x + 3", "2x", "3x"], correctAnswer: "2x + 3", difficulty: 3 },
    { text: "If sin θ = 0.5, what is θ (in degrees)?", options: ["30°", "45°", "60°", "90°"], correctAnswer: "30°", difficulty: 3 },
    { text: "Solve: 2x² - 8 = 0", options: ["x = ±2", "x = ±4", "x = ±1", "x = ±8"], correctAnswer: "x = ±2", difficulty: 3 },
    { text: "If matrix A = [1 2; 3 4], what is det(A)?", options: ["-2", "-1", "1", "2"], correctAnswer: "-2", difficulty: 4 },
    { text: "Limit of (x²-1)/(x-1) as x→1 is:", options: ["0", "1", "2", "undefined"], correctAnswer: "2", difficulty: 4 },
    { text: "In AP: 2, 5, 8, 11..., what is 10th term?", options: ["26", "29", "32", "35"], correctAnswer: "29", difficulty: 3 },
    { text: "If P(A) = 0.3, P(B) = 0.4, P(A∩B) = 0.1, find P(A∪B)", options: ["0.5", "0.6", "0.7", "0.8"], correctAnswer: "0.6", difficulty: 3 },
    { text: "Volume of sphere with radius 3 is:", options: ["36π", "27π", "18π", "12π"], correctAnswer: "36π", difficulty: 3 },
    { text: "If f(x) = x³, what is f'(2)?", options: ["6", "8", "12", "16"], correctAnswer: "12", difficulty: 3 },
    { text: "Coefficient of x² in (x+2)³ is:", options: ["6", "8", "12", "16"], correctAnswer: "12", difficulty: 3 },
    { text: "If vectors a⃗ = (1,2) and b⃗ = (3,4), find a⃗·b⃗", options: ["7", "10", "11", "14"], correctAnswer: "11", difficulty: 3 },
    { text: "Solution to dy/dx = y is:", options: ["y = ex", "y = Cex", "y = x", "y = Cx"], correctAnswer: "y = Cex", difficulty: 4 },
    { text: "If z = 3 + 4i, what is |z|?", options: ["3", "4", "5", "7"], correctAnswer: "5", difficulty: 3 },
    { text: "Integral of 1/x dx is:", options: ["ln|x| + C", "x + C", "1/x² + C", "x² + C"], correctAnswer: "ln|x| + C", difficulty: 3 }
  ],

  // BTech Computer Science - Aptitude Questions
  BTECH_COMPUTER_SCIENCE_APTITUDE: [
    { text: "What is 2¹⁰ in decimal?", options: ["512", "1024", "2048", "4096"], correctAnswer: "1024", difficulty: 2 },
    { text: "Convert binary 1101 to decimal:", options: ["11", "12", "13", "14"], correctAnswer: "13", difficulty: 2 },
    { text: "Time complexity of binary search is:", options: ["O(n)", "O(log n)", "O(n²)", "O(1)"], correctAnswer: "O(log n)", difficulty: 3 },
    { text: "Which is NOT a programming paradigm?", options: ["OOP", "Functional", "Procedural", "Sequential"], correctAnswer: "Sequential", difficulty: 2 },
    { text: "In Boolean algebra, A + A' = ?", options: ["0", "1", "A", "A'"], correctAnswer: "1", difficulty: 2 },
    { text: "What does CPU stand for?", options: ["Central Processing Unit", "Computer Processing Unit", "Central Program Unit", "Computer Program Unit"], correctAnswer: "Central Processing Unit", difficulty: 1 },
    { text: "Which sorting algorithm has best average case?", options: ["Bubble Sort", "Quick Sort", "Selection Sort", "Insertion Sort"], correctAnswer: "Quick Sort", difficulty: 3 },
    { text: "What is the size of int in most systems?", options: ["2 bytes", "4 bytes", "8 bytes", "16 bytes"], correctAnswer: "4 bytes", difficulty: 2 },
    { text: "Which data structure uses LIFO?", options: ["Queue", "Stack", "Array", "Tree"], correctAnswer: "Stack", difficulty: 2 },
    { text: "What is 255 in binary?", options: ["11111110", "11111111", "10111111", "11011111"], correctAnswer: "11111111", difficulty: 2 },
    { text: "Which protocol is used for web browsing?", options: ["FTP", "SMTP", "HTTP", "TCP"], correctAnswer: "HTTP", difficulty: 2 },
    { text: "What does SQL stand for?", options: ["Structured Query Language", "Simple Query Language", "Standard Query Language", "System Query Language"], correctAnswer: "Structured Query Language", difficulty: 1 },
    { text: "In OOP, what is encapsulation?", options: ["Hiding data", "Code reuse", "Multiple inheritance", "Dynamic binding"], correctAnswer: "Hiding data", difficulty: 2 },
    { text: "Which is fastest memory?", options: ["RAM", "Cache", "Hard Disk", "SSD"], correctAnswer: "Cache", difficulty: 2 },
    { text: "What is deadlock in OS?", options: ["Process termination", "Memory leak", "Circular wait", "CPU scheduling"], correctAnswer: "Circular wait", difficulty: 3 }
  ],

  // Graduate Science - Aptitude Questions
  GRADUATE_SCIENCE_APTITUDE: [
    { text: "Avogadro's number is approximately:", options: ["6.02 × 10²³", "6.02 × 10²²", "6.02 × 10²⁴", "6.02 × 10²¹"], correctAnswer: "6.02 × 10²³", difficulty: 2 },
    { text: "What is the pH of pure water?", options: ["6", "7", "8", "9"], correctAnswer: "7", difficulty: 2 },
    { text: "Speed of light in vacuum is:", options: ["3 × 10⁸ m/s", "3 × 10⁷ m/s", "3 × 10⁹ m/s", "3 × 10⁶ m/s"], correctAnswer: "3 × 10⁸ m/s", difficulty: 2 },
    { text: "Which element has atomic number 6?", options: ["Oxygen", "Carbon", "Nitrogen", "Boron"], correctAnswer: "Carbon", difficulty: 2 },
    { text: "Newton's second law states:", options: ["F = ma", "E = mc²", "V = IR", "PV = nRT"], correctAnswer: "F = ma", difficulty: 2 },
    { text: "DNA stands for:", options: ["Deoxyribonucleic Acid", "Dinitrogen Acid", "Dynamic Nuclear Acid", "Deoxy Nuclear Acid"], correctAnswer: "Deoxyribonucleic Acid", difficulty: 2 },
    { text: "Which gas is most abundant in atmosphere?", options: ["Oxygen", "Carbon dioxide", "Nitrogen", "Argon"], correctAnswer: "Nitrogen", difficulty: 2 },
    { text: "Planck's constant value is approximately:", options: ["6.63 × 10⁻³⁴ J·s", "6.63 × 10⁻³³ J·s", "6.63 × 10⁻³⁵ J·s", "6.63 × 10⁻³² J·s"], correctAnswer: "6.63 × 10⁻³⁴ J·s", difficulty: 3 },
    { text: "Which organelle is called powerhouse of cell?", options: ["Nucleus", "Ribosome", "Mitochondria", "Chloroplast"], correctAnswer: "Mitochondria", difficulty: 2 },
    { text: "Molecular formula of glucose is:", options: ["C₆H₁₂O₆", "C₆H₁₀O₆", "C₅H₁₂O₆", "C₆H₁₂O₅"], correctAnswer: "C₆H₁₂O₆", difficulty: 2 },
    { text: "Which law relates pressure and volume?", options: ["Boyle's Law", "Charles' Law", "Gay-Lussac's Law", "Avogadro's Law"], correctAnswer: "Boyle's Law", difficulty: 2 },
    { text: "What is the unit of electric current?", options: ["Volt", "Ampere", "Ohm", "Watt"], correctAnswer: "Ampere", difficulty: 1 },
    { text: "Which particle has no charge?", options: ["Proton", "Electron", "Neutron", "Alpha particle"], correctAnswer: "Neutron", difficulty: 2 },
    { text: "Photosynthesis occurs in:", options: ["Mitochondria", "Chloroplast", "Nucleus", "Ribosome"], correctAnswer: "Chloroplast", difficulty: 2 },
    { text: "Einstein's mass-energy relation is:", options: ["E = mc²", "E = ½mv²", "E = mgh", "E = hf"], correctAnswer: "E = mc²", difficulty: 2 }
  ],

  // Masters - Aptitude Questions (Research level)
  MASTERS_APTITUDE: [
    { text: "In research methodology, what is a null hypothesis?", options: ["Hypothesis to be tested", "Alternative hypothesis", "No relationship hypothesis", "Research question"], correctAnswer: "No relationship hypothesis", difficulty: 3 },
    { text: "What is p-value in statistics?", options: ["Probability of error", "Significance level", "Confidence interval", "Sample size"], correctAnswer: "Probability of error", difficulty: 3 },
    { text: "Which sampling method ensures representativeness?", options: ["Convenience", "Random", "Purposive", "Snowball"], correctAnswer: "Random", difficulty: 3 },
    { text: "Cronbach's alpha measures:", options: ["Validity", "Reliability", "Significance", "Correlation"], correctAnswer: "Reliability", difficulty: 3 },
    { text: "In ANOVA, F-statistic compares:", options: ["Means", "Variances", "Medians", "Modes"], correctAnswer: "Variances", difficulty: 3 },
    { text: "What is Type I error?", options: ["Accepting false null", "Rejecting true null", "Sample error", "Measurement error"], correctAnswer: "Rejecting true null", difficulty: 3 },
    { text: "Correlation coefficient ranges from:", options: ["-1 to 1", "0 to 1", "-∞ to ∞", "0 to ∞"], correctAnswer: "-1 to 1", difficulty: 2 },
    { text: "Which test is used for categorical data?", options: ["t-test", "ANOVA", "Chi-square", "Regression"], correctAnswer: "Chi-square", difficulty: 3 },
    { text: "What is effect size?", options: ["Sample size", "Practical significance", "Statistical significance", "Error rate"], correctAnswer: "Practical significance", difficulty: 3 },
    { text: "In regression, R² represents:", options: ["Correlation", "Variance explained", "Standard error", "Significance"], correctAnswer: "Variance explained", difficulty: 3 },
    { text: "What is meta-analysis?", options: ["Single study analysis", "Analysis of analyses", "Experimental design", "Survey method"], correctAnswer: "Analysis of analyses", difficulty: 3 },
    { text: "Confidence interval of 95% means:", options: ["95% accuracy", "5% error", "95% probability", "Sample confidence"], correctAnswer: "95% probability", difficulty: 3 },
    { text: "What is multicollinearity?", options: ["Multiple variables", "Variable correlation", "Multiple regression", "Variable independence"], correctAnswer: "Variable correlation", difficulty: 3 },
    { text: "In experimental design, what is randomization?", options: ["Random sampling", "Random assignment", "Random variables", "Random errors"], correctAnswer: "Random assignment", difficulty: 3 },
    { text: "What is statistical power?", options: ["Sample size", "Effect size", "Probability of detecting effect", "Significance level"], correctAnswer: "Probability of detecting effect", difficulty: 3 }
  ],

  // Psychometric questions for different categories
  CLASS_10_PSYCHOMETRIC: [
    { text: "When working in a group, I prefer to:", options: ["Lead the discussion", "Contribute ideas when asked", "Listen and support others", "Work independently"], correctAnswer: "Contribute ideas when asked", difficulty: 1 },
    { text: "When facing a difficult problem, I usually:", options: ["Ask for help immediately", "Try different approaches", "Give up quickly", "Get frustrated"], correctAnswer: "Try different approaches", difficulty: 1 },
    { text: "I feel most energized when:", options: ["Working with people", "Working alone", "Solving puzzles", "Being creative"], correctAnswer: "Working with people", difficulty: 1 },
    { text: "My ideal career would involve:", options: ["Helping others", "Creating things", "Analyzing data", "Leading teams"], correctAnswer: "Helping others", difficulty: 1 },
    { text: "When learning something new, I prefer:", options: ["Reading about it", "Watching demonstrations", "Hands-on practice", "Group discussions"], correctAnswer: "Hands-on practice", difficulty: 1 },
    { text: "I am most comfortable when:", options: ["Following established procedures", "Creating new methods", "Working with deadlines", "Having flexible schedules"], correctAnswer: "Following established procedures", difficulty: 1 },
    { text: "In stressful situations, I tend to:", options: ["Stay calm and focused", "Seek support from others", "Feel overwhelmed", "Take charge"], correctAnswer: "Stay calm and focused", difficulty: 2 },
    { text: "I am motivated by:", options: ["Recognition and praise", "Personal achievement", "Helping others succeed", "Financial rewards"], correctAnswer: "Personal achievement", difficulty: 1 },
    { text: "When making decisions, I rely on:", options: ["Logic and facts", "Intuition and feelings", "Others' opinions", "Past experiences"], correctAnswer: "Logic and facts", difficulty: 2 },
    { text: "I prefer work environments that are:", options: ["Structured and organized", "Flexible and dynamic", "Quiet and peaceful", "Social and interactive"], correctAnswer: "Structured and organized", difficulty: 1 },
    { text: "My communication style is:", options: ["Direct and to the point", "Warm and friendly", "Detailed and thorough", "Inspiring and motivational"], correctAnswer: "Direct and to the point", difficulty: 1 },
    { text: "I handle criticism by:", options: ["Taking it personally", "Using it to improve", "Ignoring it", "Defending myself"], correctAnswer: "Using it to improve", difficulty: 2 },
    { text: "I am most productive when:", options: ["Working under pressure", "Having clear deadlines", "Working at my own pace", "Collaborating with others"], correctAnswer: "Having clear deadlines", difficulty: 1 },
    { text: "My approach to risk is:", options: ["Very cautious", "Calculated risks", "Adventurous", "Risk-averse"], correctAnswer: "Calculated risks", difficulty: 2 },
    { text: "I value most in my work:", options: ["Stability and security", "Growth and learning", "Recognition and status", "Work-life balance"], correctAnswer: "Growth and learning", difficulty: 1 }
  ],

  CLASS_12_PSYCHOMETRIC: [
    { text: "When choosing a career, I prioritize:", options: ["Job security", "Personal passion", "High salary", "Social impact"], correctAnswer: "Personal passion", difficulty: 2 },
    { text: "I perform best when:", options: ["Given clear instructions", "Allowed creative freedom", "Working in teams", "Working independently"], correctAnswer: "Allowed creative freedom", difficulty: 2 },
    { text: "My leadership style is:", options: ["Authoritative", "Democratic", "Coaching", "Delegating"], correctAnswer: "Democratic", difficulty: 2 },
    { text: "When facing failure, I:", options: ["Learn from mistakes", "Blame external factors", "Give up easily", "Seek immediate solutions"], correctAnswer: "Learn from mistakes", difficulty: 2 },
    { text: "I am most interested in:", options: ["Theoretical concepts", "Practical applications", "Human behavior", "Artistic expression"], correctAnswer: "Practical applications", difficulty: 2 },
    { text: "My ideal work schedule is:", options: ["Fixed 9-5", "Flexible hours", "Night shifts", "Weekend work"], correctAnswer: "Flexible hours", difficulty: 1 },
    { text: "I handle conflict by:", options: ["Avoiding it", "Confronting directly", "Seeking mediation", "Finding compromise"], correctAnswer: "Finding compromise", difficulty: 2 },
    { text: "I am driven by:", options: ["Competition", "Collaboration", "Innovation", "Tradition"], correctAnswer: "Innovation", difficulty: 2 },
    { text: "My decision-making process is:", options: ["Quick and intuitive", "Slow and analytical", "Consensus-based", "Data-driven"], correctAnswer: "Data-driven", difficulty: 2 },
    { text: "I prefer feedback that is:", options: ["Immediate", "Detailed", "Positive only", "Constructive"], correctAnswer: "Constructive", difficulty: 2 },
    { text: "My work motivation comes from:", options: ["External rewards", "Internal satisfaction", "Peer recognition", "Authority approval"], correctAnswer: "Internal satisfaction", difficulty: 2 },
    { text: "I handle change by:", options: ["Resisting it", "Adapting quickly", "Planning carefully", "Seeking support"], correctAnswer: "Adapting quickly", difficulty: 2 },
    { text: "My problem-solving approach is:", options: ["Systematic", "Creative", "Collaborative", "Intuitive"], correctAnswer: "Systematic", difficulty: 2 },
    { text: "I value workplace culture that is:", options: ["Competitive", "Supportive", "Innovative", "Traditional"], correctAnswer: "Supportive", difficulty: 1 },
    { text: "My career goal is to:", options: ["Achieve expertise", "Gain authority", "Make impact", "Maintain balance"], correctAnswer: "Make impact", difficulty: 2 }
  ],

  BTECH_PSYCHOMETRIC: [
    { text: "In technical projects, I prefer to:", options: ["Design architecture", "Write code", "Test systems", "Manage teams"], correctAnswer: "Design architecture", difficulty: 2 },
    { text: "When debugging, I:", options: ["Use systematic approach", "Try random solutions", "Ask colleagues", "Use debugging tools"], correctAnswer: "Use systematic approach", difficulty: 2 },
    { text: "I am most interested in:", options: ["Cutting-edge technology", "Stable systems", "User experience", "Business applications"], correctAnswer: "Cutting-edge technology", difficulty: 2 },
    { text: "My ideal tech role involves:", options: ["Research & development", "Product development", "System maintenance", "Technical consulting"], correctAnswer: "Research & development", difficulty: 2 },
    { text: "I handle technical challenges by:", options: ["Breaking into smaller parts", "Seeking expert help", "Trial and error", "Following documentation"], correctAnswer: "Breaking into smaller parts", difficulty: 2 },
    { text: "In code reviews, I focus on:", options: ["Functionality", "Performance", "Readability", "Security"], correctAnswer: "Readability", difficulty: 2 },
    { text: "I prefer working with:", options: ["Latest technologies", "Proven technologies", "Open source tools", "Enterprise solutions"], correctAnswer: "Latest technologies", difficulty: 2 },
    { text: "My learning style for tech is:", options: ["Reading documentation", "Hands-on coding", "Video tutorials", "Peer discussions"], correctAnswer: "Hands-on coding", difficulty: 1 },
    { text: "I value in software:", options: ["Innovation", "Reliability", "Usability", "Scalability"], correctAnswer: "Reliability", difficulty: 2 },
    { text: "When technology fails, I:", options: ["Stay calm and troubleshoot", "Get frustrated", "Seek immediate help", "Try alternative solutions"], correctAnswer: "Stay calm and troubleshoot", difficulty: 2 },
    { text: "I prefer development methodology:", options: ["Agile", "Waterfall", "DevOps", "Lean"], correctAnswer: "Agile", difficulty: 2 },
    { text: "My career aspiration is:", options: ["Technical expert", "Team leader", "Product manager", "Entrepreneur"], correctAnswer: "Technical expert", difficulty: 2 },
    { text: "I handle technical debt by:", options: ["Ignoring it", "Gradual refactoring", "Complete rewrite", "Documenting issues"], correctAnswer: "Gradual refactoring", difficulty: 2 },
    { text: "In technical discussions, I:", options: ["Listen more", "Share opinions", "Ask questions", "Provide solutions"], correctAnswer: "Ask questions", difficulty: 2 },
    { text: "I measure success by:", options: ["Code quality", "Project completion", "User satisfaction", "Technical innovation"], correctAnswer: "User satisfaction", difficulty: 2 }
  ],

  GRADUATE_PSYCHOMETRIC: [
    { text: "In academic research, I prefer:", options: ["Theoretical studies", "Applied research", "Experimental work", "Literature review"], correctAnswer: "Applied research", difficulty: 2 },
    { text: "When presenting findings, I:", options: ["Focus on methodology", "Emphasize results", "Discuss implications", "Address limitations"], correctAnswer: "Discuss implications", difficulty: 2 },
    { text: "I am motivated by:", options: ["Academic recognition", "Practical impact", "Intellectual curiosity", "Career advancement"], correctAnswer: "Intellectual curiosity", difficulty: 2 },
    { text: "My research approach is:", options: ["Quantitative", "Qualitative", "Mixed methods", "Meta-analysis"], correctAnswer: "Mixed methods", difficulty: 2 },
    { text: "I handle research setbacks by:", options: ["Changing direction", "Persisting with modifications", "Seeking guidance", "Starting over"], correctAnswer: "Persisting with modifications", difficulty: 2 },
    { text: "In academic collaboration, I:", options: ["Lead initiatives", "Contribute expertise", "Provide support", "Coordinate activities"], correctAnswer: "Contribute expertise", difficulty: 2 },
    { text: "I value research that is:", options: ["Innovative", "Rigorous", "Practical", "Comprehensive"], correctAnswer: "Rigorous", difficulty: 2 },
    { text: "My ideal academic environment is:", options: ["Highly competitive", "Collaborative", "Independent", "Structured"], correctAnswer: "Collaborative", difficulty: 2 },
    { text: "I approach data analysis by:", options: ["Using standard methods", "Exploring new techniques", "Seeking patterns", "Validating assumptions"], correctAnswer: "Exploring new techniques", difficulty: 2 },
    { text: "When writing papers, I focus on:", options: ["Clarity", "Comprehensiveness", "Originality", "Accuracy"], correctAnswer: "Clarity", difficulty: 2 },
    { text: "I handle peer review by:", options: ["Accepting all suggestions", "Defending my work", "Selective incorporation", "Seeking clarification"], correctAnswer: "Selective incorporation", difficulty: 2 },
    { text: "My career goal is:", options: ["Academic position", "Industry research", "Consulting", "Policy making"], correctAnswer: "Academic position", difficulty: 2 },
    { text: "I manage research time by:", options: ["Strict scheduling", "Flexible planning", "Deadline-driven", "Priority-based"], correctAnswer: "Priority-based", difficulty: 2 },
    { text: "In conferences, I prefer to:", options: ["Present my work", "Network with peers", "Learn new methods", "Explore collaborations"], correctAnswer: "Learn new methods", difficulty: 2 },
    { text: "I measure research success by:", options: ["Publications", "Citations", "Impact", "Innovation"], correctAnswer: "Impact", difficulty: 2 }
  ],

  MASTERS_PSYCHOMETRIC: [
    { text: "In advanced research, I excel at:", options: ["Literature synthesis", "Methodology design", "Data interpretation", "Theory development"], correctAnswer: "Theory development", difficulty: 3 },
    { text: "My research philosophy is:", options: ["Positivist", "Interpretivist", "Pragmatic", "Critical"], correctAnswer: "Pragmatic", difficulty: 3 },
    { text: "I handle research complexity by:", options: ["Simplifying models", "Embracing uncertainty", "Seeking patterns", "Using frameworks"], correctAnswer: "Embracing uncertainty", difficulty: 3 },
    { text: "In academic debates, I:", options: ["Defend positions", "Seek synthesis", "Ask questions", "Provide evidence"], correctAnswer: "Seek synthesis", difficulty: 3 },
    { text: "I value research that:", options: ["Challenges paradigms", "Builds on existing work", "Solves practical problems", "Advances theory"], correctAnswer: "Challenges paradigms", difficulty: 3 },
    { text: "My supervision style is:", options: ["Directive", "Supportive", "Collaborative", "Hands-off"], correctAnswer: "Collaborative", difficulty: 3 },
    { text: "I approach interdisciplinary work by:", options: ["Maintaining boundaries", "Seeking integration", "Learning other fields", "Finding common ground"], correctAnswer: "Seeking integration", difficulty: 3 },
    { text: "When facing research dilemmas, I:", options: ["Consult literature", "Seek expert advice", "Trust intuition", "Use systematic analysis"], correctAnswer: "Use systematic analysis", difficulty: 3 },
    { text: "I contribute to field by:", options: ["Novel discoveries", "Methodological advances", "Theoretical insights", "Practical applications"], correctAnswer: "Theoretical insights", difficulty: 3 },
    { text: "My research impact goal is:", options: ["Academic influence", "Policy change", "Social benefit", "Scientific advancement"], correctAnswer: "Scientific advancement", difficulty: 3 },
    { text: "I handle research ethics by:", options: ["Following guidelines", "Consulting committees", "Personal judgment", "Stakeholder input"], correctAnswer: "Following guidelines", difficulty: 3 },
    { text: "In research collaboration, I:", options: ["Lead projects", "Coordinate efforts", "Contribute expertise", "Facilitate discussions"], correctAnswer: "Facilitate discussions", difficulty: 3 },
    { text: "I approach knowledge creation through:", options: ["Empirical evidence", "Logical reasoning", "Creative insight", "Systematic inquiry"], correctAnswer: "Systematic inquiry", difficulty: 3 },
    { text: "My research legacy should be:", options: ["Groundbreaking discoveries", "Methodological contributions", "Student mentorship", "Field transformation"], correctAnswer: "Field transformation", difficulty: 3 },
    { text: "I evaluate research quality by:", options: ["Rigor", "Relevance", "Originality", "Impact"], correctAnswer: "Rigor", difficulty: 3 }
  ]
};

// Function to get questions for a specific category and test type
function getQuestionsForCategory(category, testType) {
  // For aptitude tests, use specific category questions if available, otherwise use appropriate level
  if (testType === 'APTITUDE') {
    if (questionsData[`${category}_APTITUDE`]) {
      return questionsData[`${category}_APTITUDE`];
    }

    // Fallback logic based on educational level
    if (category.includes('CLASS_10_FAIL')) return questionsData.CLASS_10_FAIL_APTITUDE;
    if (category.includes('CLASS_10')) return questionsData.CLASS_10_PASS_APTITUDE;
    if (category.includes('CLASS_12')) return questionsData.CLASS_12_PASS_APTITUDE;
    if (category.includes('BTECH')) return questionsData.BTECH_COMPUTER_SCIENCE_APTITUDE;
    if (category.includes('GRADUATE')) return questionsData.GRADUATE_SCIENCE_APTITUDE;
    if (category.includes('MASTERS')) return questionsData.MASTERS_APTITUDE;

    return questionsData.CLASS_10_PASS_APTITUDE; // Default fallback
  }

  // For psychometric tests, use appropriate level
  if (testType === 'PSYCHOMETRIC') {
    if (category.includes('CLASS_10')) return questionsData.CLASS_10_PSYCHOMETRIC;
    if (category.includes('CLASS_12')) return questionsData.CLASS_12_PSYCHOMETRIC;
    if (category.includes('BTECH')) return questionsData.BTECH_PSYCHOMETRIC;
    if (category.includes('GRADUATE')) return questionsData.GRADUATE_PSYCHOMETRIC;
    if (category.includes('MASTERS')) return questionsData.MASTERS_PSYCHOMETRIC;

    return questionsData.CLASS_10_PSYCHOMETRIC; // Default fallback
  }

  return [];
}

// Function to create questions for a specific category and test type
async function createQuestionsForCategory(category, testType, questions) {
  console.log(`Creating ${questions.length} questions for ${category} - ${testType}`);

  for (const questionData of questions) {
    await prisma.question.create({
      data: {
        text: questionData.text,
        options: questionData.options,
        correctAnswer: questionData.correctAnswer,
        category: category,
        testType: testType,
        difficulty: questionData.difficulty
      }
    });
  }
}

async function main() {
  console.log('🌱 Starting database seeding...');

  try {
    // Clear existing questions
    await prisma.question.deleteMany({});
    console.log('🗑️  Cleared existing questions');

    // All categories
    const categories = [
      'CLASS_10_PASS', 'CLASS_10_FAIL', 'CLASS_12_PASS', 'CLASS_12_FAIL',
      'GRADUATE_SCIENCE', 'GRADUATE_MATHEMATICS', 'GRADUATE_SOCIAL_SCIENCES',
      'BTECH_CIVIL', 'BTECH_COMPUTER_SCIENCE', 'BTECH_MECHANICAL',
      'BTECH_ELECTRICAL', 'BTECH_OTHER', 'GRADUATE_DROPOUT',
      'MASTERS', 'MASTERS_DROPOUT'
    ];

    const testTypes = ['APTITUDE', 'PSYCHOMETRIC'];

    // Create questions for each category and test type
    for (const category of categories) {
      for (const testType of testTypes) {
        const questions = getQuestionsForCategory(category, testType);
        await createQuestionsForCategory(category, testType, questions);
      }
    }

    console.log('✅ Database seeding completed successfully!');

    // Print summary
    const totalQuestions = await prisma.question.count();
    console.log(`📊 Total questions created: ${totalQuestions}`);

    const questionsByCategory = await prisma.question.groupBy({
      by: ['category', 'testType'],
      _count: { id: true }
    });

    console.log('📈 Questions by category and type:');
    questionsByCategory.forEach(stat => {
      console.log(`   ${stat.category} - ${stat.testType}: ${stat._count.id} questions`);
    });

  } catch (error) {
    console.error('❌ Error during seeding:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
