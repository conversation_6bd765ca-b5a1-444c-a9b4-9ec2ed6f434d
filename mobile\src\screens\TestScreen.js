import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { theme } from '../theme/theme';

const TestScreen = ({ route }) => {
  const { testData } = route.params || {};

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Test Screen</Text>
      <Text style={styles.subtitle}>Test implementation coming soon!</Text>
      {testData && (
        <Text style={styles.info}>
          Test Type: {testData.testType}
          {'\n'}Questions: {testData.totalQuestions}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    paddingHorizontal: theme.spacing.lg,
  },
  title: {
    fontSize: 24,
    fontFamily: theme.fonts.bold.fontFamily,
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: theme.fonts.regular.fontFamily,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
  },
  info: {
    fontSize: 14,
    fontFamily: theme.fonts.regular.fontFamily,
    color: theme.colors.text,
    textAlign: 'center',
  },
});

export default TestScreen;
