// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  STUDENT
  PARENT
  TEACHER
}

enum StudentClass {
  CLASS_10_PASS
  CLASS_10_FAIL
  CLASS_12_PASS
  CLASS_12_FAIL
  GRADUATE_SCIENCE
  GRADUATE_MATHEMATICS
  GRADUATE_SOCIAL_SCIENCES
  BTECH_CIVIL
  BTECH_COMPUTER_SCIENCE
  BTECH_MECHANICAL
  BTECH_ELECTRICAL
  BTECH_OTHER
  GRADUATE_DROPOUT
  MASTERS
  MASTERS_DROPOUT
}

enum TestType {
  APTITUDE
  PSYCHOMETRIC
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  role      UserRole
  firstName String
  lastName  String
  phone     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Student specific fields
  studentProfile StudentProfile?
  testResults    TestResult[]

  // Parent specific fields
  children ParentChild[]

  // Teacher specific fields
  managedStudents TeacherStudent[]

  @@map("users")
}

model StudentProfile {
  id           String       @id @default(cuid())
  userId       String       @unique
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  studentClass StudentClass
  age          Int
  school       String?
  grade        String?
  interests    String[]
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  @@map("student_profiles")
}

model ParentChild {
  id       String @id @default(cuid())
  parentId String
  parent   User   @relation(fields: [parentId], references: [id], onDelete: Cascade)
  childId  String
  // Note: childId references another User with role STUDENT

  @@unique([parentId, childId])
  @@map("parent_children")
}

model TeacherStudent {
  id        String @id @default(cuid())
  teacherId String
  teacher   User   @relation(fields: [teacherId], references: [id], onDelete: Cascade)
  studentId String
  // Note: studentId references another User with role STUDENT

  @@unique([teacherId, studentId])
  @@map("teacher_students")
}

model Question {
  id            String       @id @default(cuid())
  text          String
  options       String[]
  correctAnswer String?
  category      StudentClass
  testType      TestType
  difficulty    Int          @default(1) // 1-5 scale
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt

  testAnswers TestAnswer[]

  @@map("questions")
}

model TestResult {
  id             String       @id @default(cuid())
  userId         String
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  testType       TestType
  category       StudentClass
  score          Float
  totalQuestions Int
  correctAnswers Int
  completedAt    DateTime     @default(now())

  answers TestAnswer[]

  @@map("test_results")
}

model TestAnswer {
  id             String     @id @default(cuid())
  testResultId   String
  testResult     TestResult @relation(fields: [testResultId], references: [id], onDelete: Cascade)
  questionId     String
  question       Question   @relation(fields: [questionId], references: [id], onDelete: Cascade)
  selectedAnswer String
  isCorrect      Boolean
  timeTaken      Int? // in seconds

  @@map("test_answers")
}
