import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as Animatable from 'react-native-animatable';
import Toast from 'react-native-toast-message';
import { useAuth } from '../context/AuthContext';
import { apiService } from '../services/apiService';
import { theme } from '../theme/theme';

const { width } = Dimensions.get('window');

const StudentDashboard = ({ navigation }) => {
  const { user, logout } = useAuth();
  const [availableTests, setAvailableTests] = useState([]);
  const [testResults, setTestResults] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // Load available tests
      const testsResponse = await apiService.getAvailableTests(user.id);
      if (testsResponse.success) {
        setAvailableTests(testsResponse.data.availableTests || []);
      }

      // Load test results
      const resultsResponse = await apiService.getStudentResults(user.id);
      if (resultsResponse.success) {
        setTestResults(resultsResponse.data.testResults || []);
      }

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load dashboard data',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const handleStartTest = async (testType, category) => {
    try {
      const response = await apiService.startTest(testType, category);
      if (response.success) {
        navigation.navigate('Test', {
          testData: response.data,
        });
      } else {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: response.error || 'Failed to start test',
        });
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to start test',
      });
    }
  };

  const handleLogout = async () => {
    await logout();
    Toast.show({
      type: 'success',
      text1: 'Logged Out',
      text2: 'See you soon!',
    });
  };

  const getTestTypeIcon = (testType) => {
    return testType === 'APTITUDE' ? 'school' : 'psychology';
  };

  const getTestTypeColor = (testType) => {
    return testType === 'APTITUDE' ? theme.colors.primary : theme.colors.secondary;
  };

  const renderTestCard = (test, index) => (
    <Animatable.View
      key={`${test.type}-${test.category}`}
      animation="fadeInUp"
      delay={index * 100}
      style={styles.testCard}
    >
      <View style={styles.testCardHeader}>
        <View style={[styles.testIcon, { backgroundColor: getTestTypeColor(test.type) }]}>
          <Ionicons
            name={getTestTypeIcon(test.type)}
            size={24}
            color={theme.colors.surface}
          />
        </View>
        <View style={styles.testInfo}>
          <Text style={styles.testName}>{test.name}</Text>
          <Text style={styles.testDescription}>{test.description}</Text>
        </View>
        {test.completed && (
          <View style={styles.completedBadge}>
            <Ionicons name="checkmark-circle" size={20} color={theme.colors.success} />
          </View>
        )}
      </View>
      
      <View style={styles.testDetails}>
        <View style={styles.testDetailItem}>
          <Ionicons name="time-outline" size={16} color={theme.colors.textSecondary} />
          <Text style={styles.testDetailText}>{test.duration} min</Text>
        </View>
        <View style={styles.testDetailItem}>
          <Ionicons name="help-circle-outline" size={16} color={theme.colors.textSecondary} />
          <Text style={styles.testDetailText}>{test.totalQuestions} questions</Text>
        </View>
      </View>

      <TouchableOpacity
        style={[
          styles.testButton,
          test.completed && styles.testButtonCompleted,
        ]}
        onPress={() => !test.completed && handleStartTest(test.type, test.category)}
        disabled={test.completed}
      >
        <Text style={[
          styles.testButtonText,
          test.completed && styles.testButtonTextCompleted,
        ]}>
          {test.completed ? 'Completed' : 'Start Test'}
        </Text>
        {!test.completed && (
          <Ionicons
            name="arrow-forward"
            size={16}
            color={theme.colors.surface}
            style={styles.testButtonIcon}
          />
        )}
      </TouchableOpacity>
    </Animatable.View>
  );

  const renderResultCard = (result, index) => (
    <Animatable.View
      key={result.id}
      animation="fadeInUp"
      delay={index * 100}
      style={styles.resultCard}
    >
      <View style={styles.resultHeader}>
        <Text style={styles.resultTestType}>{result.testType}</Text>
        <Text style={styles.resultDate}>
          {new Date(result.completedAt).toLocaleDateString()}
        </Text>
      </View>
      <View style={styles.resultScore}>
        <Text style={styles.resultScoreText}>{result.percentage}%</Text>
        <Text style={styles.resultScoreLabel}>
          {result.correctAnswers}/{result.totalQuestions} correct
        </Text>
      </View>
    </Animatable.View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={[theme.colors.gradientStart, theme.colors.gradientEnd]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <Text style={styles.greeting}>Hello,</Text>
            <Text style={styles.userName}>{user?.firstName} {user?.lastName}</Text>
            <Text style={styles.userClass}>
              {user?.studentProfile?.studentClass?.replace(/_/g, ' ')}
            </Text>
          </View>
          <TouchableOpacity style={styles.profileButton} onPress={handleLogout}>
            <Ionicons name="log-out-outline" size={24} color={theme.colors.surface} />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Available Tests Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Available Tests</Text>
          {availableTests.length > 0 ? (
            availableTests.map((test, index) => renderTestCard(test, index))
          ) : (
            <View style={styles.emptyState}>
              <Ionicons name="document-outline" size={48} color={theme.colors.textSecondary} />
              <Text style={styles.emptyStateText}>No tests available</Text>
            </View>
          )}
        </View>

        {/* Recent Results Section */}
        {testResults.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recent Results</Text>
            {testResults.slice(0, 3).map((result, index) => renderResultCard(result, index))}
            
            {testResults.length > 3 && (
              <TouchableOpacity style={styles.viewAllButton}>
                <Text style={styles.viewAllText}>View All Results</Text>
                <Ionicons name="arrow-forward" size={16} color={theme.colors.primary} />
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Quick Stats */}
        {testResults.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Quick Stats</Text>
            <View style={styles.statsContainer}>
              <View style={styles.statCard}>
                <Text style={styles.statNumber}>{testResults.length}</Text>
                <Text style={styles.statLabel}>Tests Taken</Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statNumber}>
                  {Math.round(
                    testResults.reduce((sum, result) => sum + result.percentage, 0) / testResults.length
                  )}%
                </Text>
                <Text style={styles.statLabel}>Average Score</Text>
              </View>
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    paddingTop: 60,
    paddingBottom: theme.spacing.lg,
    paddingHorizontal: theme.spacing.lg,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerLeft: {
    flex: 1,
  },
  greeting: {
    fontSize: 16,
    fontFamily: theme.fonts.regular.fontFamily,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  userName: {
    fontSize: 24,
    fontFamily: theme.fonts.bold.fontFamily,
    color: theme.colors.surface,
    marginTop: theme.spacing.xs,
  },
  userClass: {
    fontSize: 14,
    fontFamily: theme.fonts.medium.fontFamily,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: theme.spacing.xs,
  },
  profileButton: {
    padding: theme.spacing.sm,
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.spacing.lg,
  },
  section: {
    marginTop: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: theme.fonts.bold.fontFamily,
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  testCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    marginBottom: theme.spacing.md,
    ...theme.shadows.medium,
  },
  testCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  testIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  testInfo: {
    flex: 1,
  },
  testName: {
    fontSize: 16,
    fontFamily: theme.fonts.bold.fontFamily,
    color: theme.colors.text,
  },
  testDescription: {
    fontSize: 14,
    fontFamily: theme.fonts.regular.fontFamily,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  completedBadge: {
    marginLeft: theme.spacing.sm,
  },
  testDetails: {
    flexDirection: 'row',
    marginBottom: theme.spacing.md,
  },
  testDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: theme.spacing.lg,
  },
  testDetailText: {
    fontSize: 12,
    fontFamily: theme.fonts.regular.fontFamily,
    color: theme.colors.textSecondary,
    marginLeft: theme.spacing.xs,
  },
  testButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  testButtonCompleted: {
    backgroundColor: theme.colors.success,
  },
  testButtonText: {
    fontSize: 14,
    fontFamily: theme.fonts.bold.fontFamily,
    color: theme.colors.surface,
  },
  testButtonTextCompleted: {
    color: theme.colors.surface,
  },
  testButtonIcon: {
    marginLeft: theme.spacing.xs,
  },
  resultCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.sm,
    ...theme.shadows.small,
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  resultTestType: {
    fontSize: 14,
    fontFamily: theme.fonts.bold.fontFamily,
    color: theme.colors.text,
  },
  resultDate: {
    fontSize: 12,
    fontFamily: theme.fonts.regular.fontFamily,
    color: theme.colors.textSecondary,
  },
  resultScore: {
    alignItems: 'center',
  },
  resultScoreText: {
    fontSize: 24,
    fontFamily: theme.fonts.bold.fontFamily,
    color: theme.colors.primary,
  },
  resultScoreLabel: {
    fontSize: 12,
    fontFamily: theme.fonts.regular.fontFamily,
    color: theme.colors.textSecondary,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.lg,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: theme.spacing.xs,
    ...theme.shadows.small,
  },
  statNumber: {
    fontSize: 28,
    fontFamily: theme.fonts.bold.fontFamily,
    color: theme.colors.primary,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: theme.fonts.regular.fontFamily,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.md,
  },
  viewAllText: {
    fontSize: 14,
    fontFamily: theme.fonts.medium.fontFamily,
    color: theme.colors.primary,
    marginRight: theme.spacing.xs,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  emptyStateText: {
    fontSize: 16,
    fontFamily: theme.fonts.regular.fontFamily,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.md,
  },
});

export default StudentDashboard;
