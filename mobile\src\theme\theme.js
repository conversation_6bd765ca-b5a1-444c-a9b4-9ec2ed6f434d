import { DefaultTheme } from 'react-native-paper';
import { Platform } from 'react-native';

export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#6366F1', // Indigo
    secondary: '#EC4899', // Pink
    accent: '#10B981', // Emerald
    background: '#F8FAFC', // Slate 50
    surface: '#FFFFFF',
    text: '#1E293B', // Slate 800
    textSecondary: '#64748B', // Slate 500
    error: '#EF4444', // Red 500
    success: '#10B981', // Emerald 500
    warning: '#F59E0B', // Amber 500
    info: '#3B82F6', // Blue 500

    // Gradient colors
    gradientStart: '#6366F1',
    gradientEnd: '#8B5CF6',

    // Card colors
    cardBackground: '#FFFFFF',
    cardShadow: '#00000010',

    // Button colors
    buttonPrimary: '#6366F1',
    buttonSecondary: '#F1F5F9',
    buttonText: '#FFFFFF',
    buttonTextSecondary: '#475569',

    // Input colors
    inputBackground: '#F8FAFC',
    inputBorder: '#E2E8F0',
    inputFocus: '#6366F1',

    // Status colors
    online: '#10B981',
    offline: '#6B7280',
    pending: '#F59E0B',
  },

  fonts: {
    regular: {
      fontFamily: Platform.OS === 'ios' ? 'System' : 'Roboto',
      fontWeight: '400',
    },
    medium: {
      fontFamily: Platform.OS === 'ios' ? 'System' : 'Roboto',
      fontWeight: '600',
    },
    bold: {
      fontFamily: Platform.OS === 'ios' ? 'System' : 'Roboto',
      fontWeight: '700',
    },
  },

  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },

  borderRadius: {
    sm: 8,
    md: 12,
    lg: 16,
    xl: 24,
    full: 9999,
  },

  shadows: {
    small: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.15,
      shadowRadius: 6.27,
      elevation: 10,
    },
    large: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 8,
      },
      shadowOpacity: 0.2,
      shadowRadius: 10.32,
      elevation: 16,
    },
  },

  animations: {
    scale: {
      duration: 200,
      useNativeDriver: true,
    },
    fade: {
      duration: 300,
      useNativeDriver: true,
    },
    slide: {
      duration: 250,
      useNativeDriver: true,
    },
  },
};
