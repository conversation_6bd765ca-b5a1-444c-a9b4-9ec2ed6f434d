const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testQuestions() {
  console.log('🧪 Testing question categorization...\n');

  try {
    // Test different categories to ensure they have different questions
    const categories = [
      'CLASS_10_PASS',
      'CLASS_10_FAIL', 
      'CLASS_12_PASS',
      'BTECH_COMPUTER_SCIENCE',
      'GRADUATE_SCIENCE',
      'MASTERS'
    ];

    for (const category of categories) {
      console.log(`📚 Testing ${category}:`);
      
      // Get aptitude questions
      const aptitudeQuestions = await prisma.question.findMany({
        where: {
          category: category,
          testType: 'APTITUDE'
        },
        take: 3,
        select: {
          text: true,
          difficulty: true
        }
      });

      console.log(`  🧠 Aptitude Questions (${aptitudeQuestions.length} total):`);
      aptitudeQuestions.forEach((q, i) => {
        console.log(`    ${i + 1}. ${q.text} (Difficulty: ${q.difficulty})`);
      });

      // Get psychometric questions  
      const psychometricQuestions = await prisma.question.findMany({
        where: {
          category: category,
          testType: 'PSYCHOMETRIC'
        },
        take: 3,
        select: {
          text: true,
          difficulty: true
        }
      });

      console.log(`  🧭 Psychometric Questions (${psychometricQuestions.length} total):`);
      psychometricQuestions.forEach((q, i) => {
        console.log(`    ${i + 1}. ${q.text} (Difficulty: ${q.difficulty})`);
      });

      console.log('');
    }

    // Get overall statistics
    const stats = await prisma.question.groupBy({
      by: ['category', 'testType'],
      _count: {
        id: true
      }
    });

    console.log('📊 Question Distribution:');
    const categoryStats = {};
    stats.forEach(stat => {
      if (!categoryStats[stat.category]) {
        categoryStats[stat.category] = {};
      }
      categoryStats[stat.category][stat.testType] = stat._count.id;
    });

    Object.keys(categoryStats).forEach(category => {
      const aptitude = categoryStats[category].APTITUDE || 0;
      const psychometric = categoryStats[category].PSYCHOMETRIC || 0;
      console.log(`  ${category}: ${aptitude} aptitude, ${psychometric} psychometric`);
    });

    const totalQuestions = await prisma.question.count();
    console.log(`\n✅ Total questions in database: ${totalQuestions}`);

    // Test question uniqueness
    console.log('\n🔍 Testing question uniqueness...');
    const duplicateCheck = await prisma.question.groupBy({
      by: ['text'],
      having: {
        text: {
          _count: {
            gt: 1
          }
        }
      },
      _count: {
        text: true
      }
    });

    if (duplicateCheck.length > 0) {
      console.log(`⚠️  Found ${duplicateCheck.length} duplicate questions:`);
      duplicateCheck.forEach(dup => {
        console.log(`  - "${dup.text}" appears ${dup._count.text} times`);
      });
    } else {
      console.log('✅ All questions are unique!');
    }

  } catch (error) {
    console.error('❌ Error testing questions:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testQuestions();
