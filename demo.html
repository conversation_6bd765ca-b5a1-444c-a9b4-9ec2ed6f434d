<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pathfinder Career Counseling - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #6366F1, #8B5CF6);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #FFF;
        }
        
        .feature-card p {
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 2rem;
        }
        
        .demo-section h2 {
            margin-bottom: 1rem;
            font-size: 2rem;
        }
        
        .api-demo {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }
        
        .status {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 1rem;
        }
        
        .status.success {
            background: #10B981;
            color: white;
        }
        
        .status.pending {
            background: #F59E0B;
            color: white;
        }
        
        .categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .category {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 0.5rem;
            text-align: center;
        }
        
        .category h4 {
            margin-bottom: 0.5rem;
        }
        
        .category p {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .btn {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            margin: 0.5rem;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .tech-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧭 Pathfinder</h1>
            <p>Career Counseling Application - Development Demo</p>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <h3>🎯 Multi-Role System</h3>
                <p>Separate interfaces for Students, Parents, and Teachers with role-based authentication and access control.</p>
            </div>
            
            <div class="feature-card">
                <h3>📚 Category-Specific Tests</h3>
                <p>450 questions tailored to different educational levels from Class 10 to Masters degree programs.</p>
            </div>
            
            <div class="feature-card">
                <h3>🧠 Dual Assessment</h3>
                <p>Both Aptitude and Psychometric tests to evaluate logical reasoning and personality traits.</p>
            </div>
            
            <div class="feature-card">
                <h3>📱 Mobile-First Design</h3>
                <p>Beautiful React Native app with animated UI and cross-platform compatibility.</p>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🚀 Backend API Status</h2>
            <p>RESTful API running on Node.js + Express + Prisma + PostgreSQL</p>
            
            <div class="api-demo">
                <strong>API Health Check:</strong> http://localhost:3000/health
                <span class="status success">✅ RUNNING</span>
            </div>
            
            <div class="api-demo">
                <strong>Database:</strong> PostgreSQL (Neon Cloud)
                <span class="status success">✅ CONNECTED</span>
            </div>
            
            <div class="api-demo">
                <strong>Questions Seeded:</strong> 450 questions across all categories
                <span class="status success">✅ COMPLETE</span>
            </div>
            
            <div class="api-demo">
                <strong>Authentication:</strong> JWT-based with role management
                <span class="status success">✅ WORKING</span>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>📊 Question Categories</h2>
            <p>Each category has 15 aptitude + 15 psychometric questions (30 total per category)</p>
            
            <div class="categories">
                <div class="category">
                    <h4>Class 10 Pass</h4>
                    <p>Basic algebra, percentages, geometry</p>
                </div>
                <div class="category">
                    <h4>Class 10 Fail</h4>
                    <p>Elementary arithmetic, simple concepts</p>
                </div>
                <div class="category">
                    <h4>Class 12 Pass</h4>
                    <p>Calculus, trigonometry, matrices</p>
                </div>
                <div class="category">
                    <h4>BTech CS</h4>
                    <p>Programming, algorithms, data structures</p>
                </div>
                <div class="category">
                    <h4>Graduate Science</h4>
                    <p>Chemistry, physics, biology concepts</p>
                </div>
                <div class="category">
                    <h4>Masters</h4>
                    <p>Research methodology, statistics</p>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🛠️ Technology Stack</h2>
            <div class="tech-stack">
                <div class="tech-item">Node.js</div>
                <div class="tech-item">Express.js</div>
                <div class="tech-item">Prisma ORM</div>
                <div class="tech-item">PostgreSQL</div>
                <div class="tech-item">React Native</div>
                <div class="tech-item">Expo</div>
                <div class="tech-item">JWT Auth</div>
                <div class="tech-item">Neon DB</div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🧪 Test the API</h2>
            <p>You can test the backend API using these endpoints:</p>
            
            <div class="api-demo">
                <strong>Health Check:</strong><br>
                GET http://localhost:3000/health
            </div>
            
            <div class="api-demo">
                <strong>Register Student:</strong><br>
                POST http://localhost:3000/api/auth/register<br>
                Body: {"email":"<EMAIL>","password":"password123","firstName":"Test","lastName":"Student","role":"STUDENT","studentClass":"CLASS_10_PASS","age":16}
            </div>
            
            <div class="api-demo">
                <strong>Login:</strong><br>
                POST http://localhost:3000/api/auth/login<br>
                Body: {"email":"<EMAIL>","password":"password123"}
            </div>
        </div>
        
        <div class="demo-section">
            <h2>📱 Mobile App Features</h2>
            <p>React Native application with the following screens implemented:</p>
            
            <div class="features">
                <div class="feature-card">
                    <h3>Welcome Screen</h3>
                    <p>Animated introduction with gradient backgrounds and feature highlights</p>
                </div>
                
                <div class="feature-card">
                    <h3>Authentication</h3>
                    <p>Login and registration forms with role selection and validation</p>
                </div>
                
                <div class="feature-card">
                    <h3>Student Dashboard</h3>
                    <p>Test availability, progress tracking, and results overview</p>
                </div>
                
                <div class="feature-card">
                    <h3>Role-Based Navigation</h3>
                    <p>Different interfaces for Students, Parents, and Teachers</p>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🎯 Next Steps</h2>
            <p>To complete the full application, the following features need implementation:</p>
            
            <div class="features">
                <div class="feature-card">
                    <h3>Test Interface</h3>
                    <p>Complete question display, answer selection, and timer functionality</p>
                </div>
                
                <div class="feature-card">
                    <h3>Results Processing</h3>
                    <p>Score calculation, analytics, and career recommendations</p>
                </div>
                
                <div class="feature-card">
                    <h3>Parent Features</h3>
                    <p>Child linking, progress monitoring, and communication tools</p>
                </div>
                
                <div class="feature-card">
                    <h3>Teacher Dashboard</h3>
                    <p>Question management, student oversight, and analytics</p>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 3rem;">
            <a href="http://localhost:3000/health" class="btn" target="_blank">🔗 Test Backend API</a>
            <a href="https://github.com" class="btn" target="_blank">📱 View Mobile Code</a>
        </div>
    </div>
</body>
</html>
