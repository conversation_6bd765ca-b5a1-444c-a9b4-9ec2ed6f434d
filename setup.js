#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up Pathfinder Career Counseling Application...\n');

// Function to run commands
function runCommand(command, description) {
  console.log(`📦 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completed\n`);
  } catch (error) {
    console.error(`❌ Error during ${description}:`, error.message);
    process.exit(1);
  }
}

// Function to check if command exists
function commandExists(command) {
  try {
    execSync(`${command} --version`, { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
}

// Check prerequisites
console.log('🔍 Checking prerequisites...');

if (!commandExists('node')) {
  console.error('❌ Node.js is not installed. Please install Node.js from https://nodejs.org/');
  process.exit(1);
}

if (!commandExists('npm')) {
  console.error('❌ npm is not installed. Please install npm.');
  process.exit(1);
}

console.log('✅ Prerequisites check passed\n');

// Backend setup
console.log('🔧 Setting up backend...');

// Install backend dependencies
runCommand('npm install', 'Installing backend dependencies');

// Generate Prisma client
runCommand('npx prisma generate', 'Generating Prisma client');

// Run database migrations
runCommand('npx prisma migrate dev --name init', 'Running database migrations');

// Seed the database
runCommand('npm run prisma:seed', 'Seeding database with sample questions');

console.log('🎉 Backend setup completed!\n');

// Mobile app setup
console.log('📱 Setting up mobile application...');

// Check if mobile directory exists
if (!fs.existsSync('./mobile')) {
  console.error('❌ Mobile directory not found. Please ensure the mobile app files are in the ./mobile directory.');
  process.exit(1);
}

// Change to mobile directory and install dependencies
process.chdir('./mobile');

// Check if Expo CLI is installed globally
if (!commandExists('expo')) {
  console.log('📦 Installing Expo CLI globally...');
  runCommand('npm install -g @expo/cli', 'Installing Expo CLI');
}

// Install mobile dependencies
runCommand('npm install', 'Installing mobile app dependencies');

// Go back to root directory
process.chdir('..');

console.log('🎉 Mobile app setup completed!\n');

// Display next steps
console.log('🎯 Setup completed successfully!\n');
console.log('📋 Next steps:');
console.log('');
console.log('1. Start the backend server:');
console.log('   npm run dev');
console.log('');
console.log('2. In a new terminal, start the mobile app:');
console.log('   cd mobile');
console.log('   npm start');
console.log('');
console.log('3. Use the Expo Go app on your phone to scan the QR code');
console.log('   or press "a" for Android emulator, "i" for iOS simulator');
console.log('');
console.log('🌐 API Health Check: http://localhost:3000/health');
console.log('📊 Database Admin: npx prisma studio');
console.log('');
console.log('🎉 Happy coding with Pathfinder!');
console.log('');
console.log('📚 For more information, check the README.md file');
console.log('🐛 Report issues: https://github.com/your-repo/pathfinder/issues');
console.log('');
