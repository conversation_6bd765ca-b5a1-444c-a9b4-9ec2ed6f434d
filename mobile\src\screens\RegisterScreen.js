import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Picker } from '@react-native-picker/picker';
import * as Animatable from 'react-native-animatable';
import Toast from 'react-native-toast-message';
import { useAuth } from '../context/AuthContext';
import { theme } from '../theme/theme';

const STUDENT_CLASSES = [
  { label: 'Class 10 - Pass', value: 'CLASS_10_PASS' },
  { label: 'Class 10 - Fail', value: 'CLASS_10_FAIL' },
  { label: 'Class 12 - Pass', value: 'CLASS_12_PASS' },
  { label: 'Class 12 - Fail', value: 'CLASS_12_FAIL' },
  { label: 'Graduate - Science', value: 'GRADUATE_SCIENCE' },
  { label: 'Graduate - Mathematics', value: 'GRADUATE_MATHEMATICS' },
  { label: 'Graduate - Social Sciences', value: 'GRADUATE_SOCIAL_SCIENCES' },
  { label: 'BTech - Civil', value: 'BTECH_CIVIL' },
  { label: 'BTech - Computer Science', value: 'BTECH_COMPUTER_SCIENCE' },
  { label: 'BTech - Mechanical', value: 'BTECH_MECHANICAL' },
  { label: 'BTech - Electrical', value: 'BTECH_ELECTRICAL' },
  { label: 'BTech - Other', value: 'BTECH_OTHER' },
  { label: 'Graduate Dropout', value: 'GRADUATE_DROPOUT' },
  { label: 'Masters', value: 'MASTERS' },
  { label: 'Masters Dropout', value: 'MASTERS_DROPOUT' },
];

const RegisterScreen = ({ navigation }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    role: 'STUDENT',
    // Student specific fields
    studentClass: 'CLASS_10_PASS',
    age: '',
    school: '',
    grade: '',
  });
  
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const { register } = useAuth();
  const shakeAnimation = useRef(new Animated.Value(0)).current;

  const validateForm = () => {
    const newErrors = {};

    // Basic validation
    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }
    
    if (!formData.password.trim()) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    // Student specific validation
    if (formData.role === 'STUDENT') {
      if (!formData.age.trim()) {
        newErrors.age = 'Age is required';
      } else if (isNaN(formData.age) || parseInt(formData.age) < 10 || parseInt(formData.age) > 100) {
        newErrors.age = 'Please enter a valid age (10-100)';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const shakeForm = () => {
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: 10, duration: 100, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 100, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 100, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 100, useNativeDriver: true }),
    ]).start();
  };

  const handleRegister = async () => {
    if (!validateForm()) {
      shakeForm();
      return;
    }

    setIsLoading(true);
    try {
      const registrationData = {
        ...formData,
        age: formData.role === 'STUDENT' ? parseInt(formData.age) : undefined,
        interests: [], // Can be expanded later
      };

      const result = await register(registrationData);
      
      if (result.success) {
        Toast.show({
          type: 'success',
          text1: 'Registration Successful!',
          text2: 'Welcome to Pathfinder',
        });
      } else {
        Toast.show({
          type: 'error',
          text1: 'Registration Failed',
          text2: result.error || 'Please try again',
        });
        shakeForm();
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Something went wrong. Please try again.',
      });
      shakeForm();
    } finally {
      setIsLoading(false);
    }
  };

  const updateFormData = (field, value) => {
    setFormData({ ...formData, [field]: value });
    if (errors[field]) {
      setErrors({ ...errors, [field]: null });
    }
  };

  const renderInput = (field, placeholder, icon, options = {}) => (
    <View style={styles.inputContainer}>
      <Text style={styles.inputLabel}>{placeholder}</Text>
      <View style={[styles.inputWrapper, errors[field] && styles.inputError]}>
        <Ionicons
          name={icon}
          size={20}
          color={theme.colors.textSecondary}
          style={styles.inputIcon}
        />
        <TextInput
          style={styles.textInput}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.textSecondary}
          value={formData[field]}
          onChangeText={(text) => updateFormData(field, text)}
          {...options}
        />
        {(field === 'password' || field === 'confirmPassword') && (
          <TouchableOpacity
            onPress={() => field === 'password' ? setShowPassword(!showPassword) : setShowConfirmPassword(!showConfirmPassword)}
            style={styles.eyeIcon}
          >
            <Ionicons
              name={(field === 'password' ? showPassword : showConfirmPassword) ? 'eye-outline' : 'eye-off-outline'}
              size={20}
              color={theme.colors.textSecondary}
            />
          </TouchableOpacity>
        )}
      </View>
      {errors[field] && <Text style={styles.errorText}>{errors[field]}</Text>}
    </View>
  );

  return (
    <LinearGradient
      colors={[theme.colors.gradientStart, theme.colors.gradientEnd]}
      style={styles.container}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <Animatable.View
            animation="slideInDown"
            duration={1000}
            style={styles.header}
          >
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="arrow-back" size={24} color={theme.colors.surface} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Create Account</Text>
            <Text style={styles.headerSubtitle}>Join Pathfinder today</Text>
          </Animatable.View>

          {/* Form */}
          <Animated.View
            style={[
              styles.formContainer,
              { transform: [{ translateX: shakeAnimation }] },
            ]}
          >
            <Animatable.View
              animation="slideInUp"
              duration={1000}
              delay={300}
              style={styles.form}
            >
              {/* Role Selection */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>I am a</Text>
                <View style={styles.roleContainer}>
                  {['STUDENT', 'PARENT', 'TEACHER'].map((role) => (
                    <TouchableOpacity
                      key={role}
                      style={[
                        styles.roleButton,
                        formData.role === role && styles.roleButtonActive,
                      ]}
                      onPress={() => updateFormData('role', role)}
                    >
                      <Text
                        style={[
                          styles.roleButtonText,
                          formData.role === role && styles.roleButtonTextActive,
                        ]}
                      >
                        {role.charAt(0) + role.slice(1).toLowerCase()}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Basic Information */}
              {renderInput('firstName', 'First Name', 'person-outline')}
              {renderInput('lastName', 'Last Name', 'person-outline')}
              {renderInput('email', 'Email Address', 'mail-outline', {
                keyboardType: 'email-address',
                autoCapitalize: 'none',
                autoCorrect: false,
              })}
              {renderInput('phone', 'Phone Number (Optional)', 'call-outline', {
                keyboardType: 'phone-pad',
              })}

              {/* Student Specific Fields */}
              {formData.role === 'STUDENT' && (
                <>
                  <View style={styles.inputContainer}>
                    <Text style={styles.inputLabel}>Educational Level</Text>
                    <View style={styles.pickerWrapper}>
                      <Picker
                        selectedValue={formData.studentClass}
                        onValueChange={(value) => updateFormData('studentClass', value)}
                        style={styles.picker}
                      >
                        {STUDENT_CLASSES.map((item) => (
                          <Picker.Item
                            key={item.value}
                            label={item.label}
                            value={item.value}
                          />
                        ))}
                      </Picker>
                    </View>
                  </View>

                  {renderInput('age', 'Age', 'calendar-outline', {
                    keyboardType: 'numeric',
                  })}
                  {renderInput('school', 'School/College (Optional)', 'school-outline')}
                  {renderInput('grade', 'Current Grade (Optional)', 'ribbon-outline')}
                </>
              )}

              {/* Password Fields */}
              {renderInput('password', 'Password', 'lock-closed-outline', {
                secureTextEntry: !showPassword,
              })}
              {renderInput('confirmPassword', 'Confirm Password', 'lock-closed-outline', {
                secureTextEntry: !showConfirmPassword,
              })}

              {/* Register Button */}
              <TouchableOpacity
                style={[styles.registerButton, isLoading && styles.registerButtonDisabled]}
                onPress={handleRegister}
                disabled={isLoading}
                activeOpacity={0.8}
              >
                {isLoading ? (
                  <Text style={styles.registerButtonText}>Creating Account...</Text>
                ) : (
                  <>
                    <Text style={styles.registerButtonText}>Create Account</Text>
                    <Ionicons
                      name="arrow-forward"
                      size={20}
                      color={theme.colors.surface}
                      style={styles.buttonIcon}
                    />
                  </>
                )}
              </TouchableOpacity>

              {/* Login Link */}
              <View style={styles.loginContainer}>
                <Text style={styles.loginText}>Already have an account? </Text>
                <TouchableOpacity onPress={() => navigation.navigate('Login')}>
                  <Text style={styles.loginLink}>Sign In</Text>
                </TouchableOpacity>
              </View>
            </Animatable.View>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: theme.spacing.lg,
    paddingBottom: theme.spacing.xl,
  },
  header: {
    paddingTop: 60,
    paddingBottom: theme.spacing.lg,
  },
  backButton: {
    marginBottom: theme.spacing.lg,
  },
  headerTitle: {
    fontSize: 32,
    fontFamily: theme.fonts.bold.fontFamily,
    color: theme.colors.surface,
    marginBottom: theme.spacing.sm,
  },
  headerSubtitle: {
    fontSize: 16,
    fontFamily: theme.fonts.regular.fontFamily,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  formContainer: {
    flex: 1,
  },
  form: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.xl,
    padding: theme.spacing.lg,
    ...theme.shadows.large,
  },
  inputContainer: {
    marginBottom: theme.spacing.lg,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: theme.fonts.medium.fontFamily,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.inputBackground,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
    paddingHorizontal: theme.spacing.md,
    height: 50,
  },
  inputError: {
    borderColor: theme.colors.error,
  },
  inputIcon: {
    marginRight: theme.spacing.sm,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: theme.fonts.regular.fontFamily,
    color: theme.colors.text,
  },
  eyeIcon: {
    padding: theme.spacing.sm,
  },
  errorText: {
    fontSize: 12,
    fontFamily: theme.fonts.regular.fontFamily,
    color: theme.colors.error,
    marginTop: theme.spacing.xs,
  },
  roleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  roleButton: {
    flex: 1,
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
    marginHorizontal: theme.spacing.xs,
    alignItems: 'center',
  },
  roleButtonActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  roleButtonText: {
    fontSize: 14,
    fontFamily: theme.fonts.medium.fontFamily,
    color: theme.colors.textSecondary,
  },
  roleButtonTextActive: {
    color: theme.colors.surface,
  },
  pickerWrapper: {
    backgroundColor: theme.colors.inputBackground,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.inputBorder,
  },
  picker: {
    height: 50,
  },
  registerButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.lg,
    ...theme.shadows.medium,
  },
  registerButtonDisabled: {
    opacity: 0.7,
  },
  registerButtonText: {
    fontSize: 16,
    fontFamily: theme.fonts.bold.fontFamily,
    color: theme.colors.surface,
  },
  buttonIcon: {
    marginLeft: theme.spacing.sm,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginText: {
    fontSize: 14,
    fontFamily: theme.fonts.regular.fontFamily,
    color: theme.colors.textSecondary,
  },
  loginLink: {
    fontSize: 14,
    fontFamily: theme.fonts.bold.fontFamily,
    color: theme.colors.primary,
  },
});

export default RegisterScreen;
